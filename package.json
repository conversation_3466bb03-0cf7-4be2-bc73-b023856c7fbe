{"name": "simperum2", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@primeuix/themes": "^1.1.1", "@primevue/themes": "^4.3.4", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.4", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "3.5.3", "tailwindcss": "^3.4.17", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-eslint-parser": "^10.1.3"}}