<template>
  <div class="responsive-layout h-screen flex flex-col">
    <!-- Title Section -->
    <header
      class="title-section bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between"
    >
      <div class="title-left">
        <h1 class="text-2xl font-bold text-gray-800">Dashboard</h1>
        <p class="text-sm text-gray-600">Manage your content and settings</p>
      </div>
      <div class="title-right flex items-center gap-2">
        <Button
          v-tooltip="'Notifications'"
          icon="pi pi-bell"
          severity="secondary"
          text
          rounded
          @click="showNotifications"
        />
        <Button
          v-tooltip="'Settings'"
          icon="pi pi-cog"
          severity="secondary"
          text
          rounded
          @click="showSettings"
        />
        <Button
          v-tooltip="'Profile'"
          icon="pi pi-user"
          severity="secondary"
          text
          rounded
          @click="showProfile"
        />
        <Button
          v-tooltip="'Logout'"
          icon="pi pi-sign-out"
          severity="danger"
          text
          rounded
          @click="logout"
        />
      </div>
    </header>

    <!-- Main Content Area -->
    <div class="content-area flex flex-1 overflow-hidden">
      <!-- Side List Section -->
      <aside
        :class="[
          'side-list-section bg-gray-50 border-r border-gray-200 transition-all duration-300 ease-in-out',
          'lg:w-80 lg:block',
          showSidebar ? 'w-full block' : 'w-0 hidden',
          'md:w-80 md:block',
        ]"
      >
        <div class="p-4">
          <div class="flex items-center justify-between mb-4 lg:hidden">
            <h2 class="text-lg font-semibold text-gray-800">Menu</h2>
            <Button
              icon="pi pi-times"
              severity="secondary"
              text
              rounded
              class="lg:hidden"
              @click="hideSidebar"
            />
          </div>

          <div class="space-y-2">
            <div
              v-for="item in menuItems"
              :key="item.id"
              :class="[
                'menu-item p-3 rounded-lg cursor-pointer transition-all duration-200',
                'hover:bg-blue-50 hover:border-blue-200',
                selectedItem?.id === item.id
                  ? 'bg-blue-100 border-blue-300 border-2'
                  : 'bg-white border border-gray-200',
              ]"
              @click="selectItem(item)"
            >
              <div class="flex items-center gap-3">
                <i :class="[item.icon, 'text-lg']" :style="{ color: item.color }"></i>
                <div class="flex-1">
                  <h3 class="font-medium text-gray-800">{{ item.title }}</h3>
                  <p class="text-sm text-gray-600">{{ item.description }}</p>
                </div>
                <Badge v-if="item.badge" :value="item.badge" severity="info" />
              </div>
            </div>
          </div>
        </div>
      </aside>

      <!-- Main Section -->
      <main
        :class="[
          'main-section flex-1 bg-white overflow-auto transition-all duration-300 ease-in-out',
          !showSidebar || 'lg:block' ? 'block' : 'hidden lg:block',
        ]"
      >
        <div class="p-6">
          <!-- Mobile Menu Button -->
          <Button
            v-if="!showSidebar"
            icon="pi pi-bars"
            severity="secondary"
            text
            rounded
            class="lg:hidden mb-4"
            @click="showSidebar = true"
          />

          <!-- Dynamic Content Based on Selected Item -->
          <div v-if="selectedItem" class="content-wrapper">
            <div class="mb-6">
              <div class="flex items-center gap-3 mb-2">
                <i
                  :class="[selectedItem.icon, 'text-2xl']"
                  :style="{ color: selectedItem.color }"
                ></i>
                <h2 class="text-3xl font-bold text-gray-800">{{ selectedItem.title }}</h2>
              </div>
              <p class="text-gray-600">{{ selectedItem.fullDescription }}</p>
            </div>

            <!-- Content Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <Card
                v-for="card in selectedItem.content"
                :key="card.id"
                class="hover:shadow-lg transition-shadow"
              >
                <template #header>
                  <div
                    class="bg-gradient-to-r from-blue-500 to-purple-600 h-32 flex items-center justify-center"
                  >
                    <i :class="[card.icon, 'text-4xl text-white']"></i>
                  </div>
                </template>
                <template #title>{{ card.title }}</template>
                <template #content>
                  <p class="text-gray-600 mb-4">{{ card.description }}</p>
                  <div class="flex justify-between items-center">
                    <span class="text-2xl font-bold text-blue-600">{{ card.value }}</span>
                    <Button :label="card.action" size="small" />
                  </div>
                </template>
              </Card>
            </div>

            <!-- Additional Content Section -->
            <div class="bg-gray-50 rounded-lg p-6">
              <h3 class="text-xl font-semibold mb-4">Recent Activity</h3>
              <div class="space-y-3">
                <div
                  v-for="activity in selectedItem.activities"
                  :key="activity.id"
                  class="flex items-center gap-3 p-3 bg-white rounded-lg border"
                >
                  <Avatar :icon="activity.icon" class="bg-blue-100 text-blue-600" />
                  <div class="flex-1">
                    <p class="font-medium">{{ activity.title }}</p>
                    <p class="text-sm text-gray-600">{{ activity.time }}</p>
                  </div>
                  <Button icon="pi pi-chevron-right" text rounded size="small" />
                </div>
              </div>
            </div>
          </div>

          <!-- Default Content When No Item Selected -->
          <div v-else class="text-center py-12">
            <i class="pi pi-inbox text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">Select an item from the menu</h3>
            <p class="text-gray-500">Choose an option from the sidebar to view its content</p>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Button from 'primevue/button'
import Card from 'primevue/card'
import Badge from 'primevue/badge'
import Avatar from 'primevue/avatar'

// Reactive data
const showSidebar = ref(false)
const selectedItem = ref(null)

// Menu items data
const menuItems = ref([
  {
    id: 1,
    title: 'Analytics',
    description: 'View performance metrics',
    icon: 'pi pi-chart-line',
    color: '#3B82F6',
    badge: '5',
    fullDescription:
      'Comprehensive analytics dashboard showing key performance indicators and metrics.',
    content: [
      {
        id: 1,
        title: 'Total Users',
        description: 'Active users this month',
        value: '12,345',
        action: 'View Details',
        icon: 'pi pi-users',
      },
      {
        id: 2,
        title: 'Revenue',
        description: 'Monthly revenue',
        value: '$45,678',
        action: 'View Report',
        icon: 'pi pi-dollar',
      },
      {
        id: 3,
        title: 'Conversion',
        description: 'Conversion rate',
        value: '3.2%',
        action: 'Optimize',
        icon: 'pi pi-percentage',
      },
    ],
    activities: [
      { id: 1, title: 'New user registration spike', time: '2 hours ago', icon: 'pi pi-user-plus' },
      { id: 2, title: 'Revenue target achieved', time: '4 hours ago', icon: 'pi pi-check-circle' },
      { id: 3, title: 'Analytics report generated', time: '6 hours ago', icon: 'pi pi-file' },
    ],
  },
  {
    id: 2,
    title: 'Projects',
    description: 'Manage your projects',
    icon: 'pi pi-folder',
    color: '#10B981',
    badge: '12',
    fullDescription:
      'Project management hub for tracking progress, deadlines, and team collaboration.',
    content: [
      {
        id: 1,
        title: 'Active Projects',
        description: 'Currently running',
        value: '8',
        action: 'View All',
        icon: 'pi pi-play',
      },
      {
        id: 2,
        title: 'Completed',
        description: 'This quarter',
        value: '24',
        action: 'Archive',
        icon: 'pi pi-check',
      },
      {
        id: 3,
        title: 'Team Members',
        description: 'Total contributors',
        value: '156',
        action: 'Manage',
        icon: 'pi pi-users',
      },
    ],
    activities: [
      { id: 1, title: 'Project Alpha milestone reached', time: '1 hour ago', icon: 'pi pi-flag' },
      { id: 2, title: 'New team member added', time: '3 hours ago', icon: 'pi pi-user-plus' },
      {
        id: 3,
        title: 'Project Beta deadline updated',
        time: '5 hours ago',
        icon: 'pi pi-calendar',
      },
    ],
  },
  {
    id: 3,
    title: 'Users',
    description: 'User management',
    icon: 'pi pi-users',
    color: '#8B5CF6',
    badge: '3',
    fullDescription:
      'Complete user management system for handling accounts, permissions, and user data.',
    content: [
      {
        id: 1,
        title: 'Total Users',
        description: 'Registered accounts',
        value: '2,847',
        action: 'View List',
        icon: 'pi pi-user',
      },
      {
        id: 2,
        title: 'Active Today',
        description: 'Users online now',
        value: '342',
        action: 'View Active',
        icon: 'pi pi-circle-fill',
      },
      {
        id: 3,
        title: 'New Signups',
        description: 'This week',
        value: '89',
        action: 'Review',
        icon: 'pi pi-user-plus',
      },
    ],
    activities: [
      { id: 1, title: 'Bulk user import completed', time: '30 minutes ago', icon: 'pi pi-upload' },
      { id: 2, title: 'User permissions updated', time: '2 hours ago', icon: 'pi pi-shield' },
      { id: 3, title: 'Password reset requests', time: '4 hours ago', icon: 'pi pi-key' },
    ],
  },
  {
    id: 4,
    title: 'Settings',
    description: 'System configuration',
    icon: 'pi pi-cog',
    color: '#F59E0B',
    fullDescription: 'System settings and configuration options for customizing your application.',
    content: [
      {
        id: 1,
        title: 'System Health',
        description: 'Overall status',
        value: '98%',
        action: 'Check Details',
        icon: 'pi pi-heart',
      },
      {
        id: 2,
        title: 'Storage Used',
        description: 'Of total capacity',
        value: '67%',
        action: 'Manage',
        icon: 'pi pi-database',
      },
      {
        id: 3,
        title: 'API Calls',
        description: 'This month',
        value: '1.2M',
        action: 'View Logs',
        icon: 'pi pi-server',
      },
    ],
    activities: [
      { id: 1, title: 'System backup completed', time: '1 hour ago', icon: 'pi pi-cloud-upload' },
      { id: 2, title: 'Security scan finished', time: '6 hours ago', icon: 'pi pi-shield' },
      { id: 3, title: 'Database optimization', time: '12 hours ago', icon: 'pi pi-database' },
    ],
  },
  {
    id: 5,
    title: 'Reports',
    description: 'Generate and view reports',
    icon: 'pi pi-file-pdf',
    color: '#EF4444',
    badge: '2',
    fullDescription:
      'Comprehensive reporting system for generating insights and business intelligence.',
    content: [
      {
        id: 1,
        title: 'Monthly Reports',
        description: 'Generated this month',
        value: '15',
        action: 'View All',
        icon: 'pi pi-calendar',
      },
      {
        id: 2,
        title: 'Custom Reports',
        description: 'User created',
        value: '7',
        action: 'Create New',
        icon: 'pi pi-plus',
      },
      {
        id: 3,
        title: 'Scheduled',
        description: 'Auto-generated',
        value: '23',
        action: 'Manage',
        icon: 'pi pi-clock',
      },
    ],
    activities: [
      { id: 1, title: 'Monthly sales report ready', time: '2 hours ago', icon: 'pi pi-file-pdf' },
      { id: 2, title: 'Custom report scheduled', time: '5 hours ago', icon: 'pi pi-calendar-plus' },
      { id: 3, title: 'Report template updated', time: '8 hours ago', icon: 'pi pi-file-edit' },
    ],
  },
])

// Methods
const selectItem = (item) => {
  selectedItem.value = item
  // Hide sidebar on mobile after selection
  if (window.innerWidth < 1024) {
    showSidebar.value = false
  }
}

const hideSidebar = () => {
  showSidebar.value = false
}

const showNotifications = () => {
  console.log('Show notifications')
}

const showSettings = () => {
  console.log('Show settings')
}

const showProfile = () => {
  console.log('Show profile')
}

const logout = () => {
  console.log('Logout')
}

// Initialize with first item selected
onMounted(() => {
  if (menuItems.value.length > 0) {
    selectedItem.value = menuItems.value[0]
  }

  // Show sidebar by default on desktop
  if (window.innerWidth >= 1024) {
    showSidebar.value = true
  }
})
</script>

<style scoped>
.responsive-layout {
  font-family: 'Inter', sans-serif;
}

.menu-item {
  transition: all 0.2s ease-in-out;
}

.menu-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.content-wrapper {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar */
.main-section::-webkit-scrollbar {
  width: 6px;
}

.main-section::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.main-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
