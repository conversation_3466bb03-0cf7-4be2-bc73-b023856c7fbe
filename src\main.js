import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Import PrimeVue
import PrimeVue from 'primevue/config'
// Import PrimeIcons
import 'primeicons/primeicons.css'
import Aura from '@primeuix/themes/aura'
// import Lara from '@primeuix/themes/lara';

// Import PrimeVue components
import Button from 'primevue/button'
import Card from 'primevue/card'
import Badge from 'primevue/badge'
import Avatar from 'primevue/avatar'
import Tooltip from 'primevue/tooltip'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
// Use PrimeVue
app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      darkModeSelector: false,
    },
  },
})

// Register PrimeVue components globally
app.component('Button', Button)
app.component('Card', Card)
app.component('Badge', Badge)
app.component('Avatar', Avatar)

// Register directives
app.directive('tooltip', Tooltip)

app.mount('#app')
